/**
 * Global styles for consistent UI across the website
 */

/* Font styles */
body {
  font-family: 'Roboto', 'Playfair Display', sans-serif;
  color: #333;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
}

/* Brand colors */
:root {
  --primary-color: #A67B5B;
  --primary-dark: #8A5A3B;
  --primary-light: #C9B6A5;
  --text-color: #333333;
  --text-light: #666666;
  --background-light: #F9F7F5;
  --background-dark: #2D2D2D;
  --accent-color: #D4AF37;
}

/* Button styles */
.btn-primary {
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: white;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
  background-color: var(--background-light);
  color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-1px);
}

/* Section styles */
.section-title {
  font-size: 2rem;
  font-weight: 300;
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 4rem;
  height: 2px;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.section-title:hover::after {
  width: 6rem;
}

/* Card styles */
.card {
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* Form styles */
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(166, 123, 91, 0.2);
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Utility classes */
.text-primary {
  color: var(--primary-color);
}

.bg-primary {
  background-color: var(--primary-color);
}

.border-primary {
  border-color: var(--primary-color);
}

.hover-primary:hover {
  color: var(--primary-color);
}

/* Breadcrumb styles */
.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 0.75rem 0;
  margin-bottom: 1rem;
  list-style: none;
  background-color: var(--background-light);
  border-radius: 0.25rem;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.5rem;
  color: var(--text-light);
  content: "/";
}

.breadcrumb-item a {
  color: var(--text-light);
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-item a:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.breadcrumb-item.active {
  color: var(--primary-color);
  font-weight: 500;
}

/* Responsive container */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }
}
