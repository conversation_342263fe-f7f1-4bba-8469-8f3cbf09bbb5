.title {
    text-align: center;
    /* <PERSON>r le texte */
    font-size: 2.3rem;
    /* Ajuster la taille */
    font-weight: 200;
    margin-bottom: 20px;
    position: relative;
}

.title em {
    display: inline-block;
    padding-bottom: 10px;
    font-style: oblique;
}

.title em span {
    position: relative;
    display: inline-block;
    padding-bottom: 5px;
}

.title em span::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 6px;
    background-color: rgb(140, 99, 5);
    bottom: -5px;
    left: 0;
}

.title i {
    display: block;
    font-size: 1.0rem;
    color: rgba(0, 0, 0, 0.7);
    margin-top: 10px;
    font-style: italic;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #D4AF37;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #b69329;
}

/* Override navbar hover effects to keep only the active underline */
.nav-item {
    position: relative;
}

.nav-item::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    bottom: 0;
    left: 0;
    background-color: transparent;
    transition: none;
    display: none !important;
}

.nav-item:hover::after {
    width: 0 !important;
    opacity: 0 !important;
    display: none !important;
}

/* Disable underline animation for navbar items */
.nav-item.underline-animation::after,
.nav-item .underline-animation::after {
    display: none !important;
    width: 0 !important;
    opacity: 0 !important;
}

/* Navbar and footer styling */
/* Ensure consistent styling for navbar and footer across all pages */