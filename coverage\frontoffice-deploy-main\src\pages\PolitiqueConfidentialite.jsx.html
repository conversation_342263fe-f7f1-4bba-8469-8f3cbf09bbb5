
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for frontoffice-deploy-main/src/pages/PolitiqueConfidentialite.jsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">frontoffice-deploy-main/src/pages</a> PolitiqueConfidentialite.jsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/32</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/32</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import Chatbot from "@/Components/Chatbot";<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >import React from "react";</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const PolitiqueConfidentialite = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="min-h-screen bg-gradient-to-br from-[#f7f3ef] to-[#fff] py-12 px-4 sm:px-8 md:px-20 lg:px-48"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="max-w-3xl mx-auto bg-white rounded-2xl shadow-xl p-8 md:p-12"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;Chatbot /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;h1 className="text-3xl md:text-4xl font-bold text-[#A67B5B] mb-6 text-center"&gt;Politique de confidentialité&lt;/h1&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;p className="text-gray-700 mb-4 text-justify"&gt;</span>
&nbsp;
          Nous accordons une grande importance à la protection de vos données personnelles. Cette politique explique comment nous collectons, utilisons et protégeons vos informations lors de votre navigation sur Ji-line &amp; Carré Blanc Paris Sfax.
<span class="cstat-no" title="statement not covered" >        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;h2 className="text-xl font-semibold text-[#A67B5B] mt-6 mb-2"&gt;1. Collecte des données&lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;p className="text-gray-700 mb-3 text-justify"&gt;</span>
          Nous collectons uniquement les informations nécessaires à la gestion de vos commandes, à la création de votre compte et à l'amélioration de nos services (nom, email, adresse, téléphone, historique d'achat).
<span class="cstat-no" title="statement not covered" >        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;h2 className="text-xl font-semibold text-[#A67B5B] mt-6 mb-2"&gt;2. Utilisation des données&lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;p className="text-gray-700 mb-3 text-justify"&gt;</span>
          Vos données sont utilisées pour traiter vos commandes, vous contacter, personnaliser votre expérience et vous informer de nos offres. Elles ne sont jamais revendues à des tiers.
<span class="cstat-no" title="statement not covered" >        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;h2 className="text-xl font-semibold text-[#A67B5B] mt-6 mb-2"&gt;3. Sécurité&lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;p className="text-gray-700 mb-3 text-justify"&gt;</span>
          Nous mettons en œuvre des mesures de sécurité pour protéger vos données contre tout accès non autorisé, altération ou divulgation.
<span class="cstat-no" title="statement not covered" >        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;h2 className="text-xl font-semibold text-[#A67B5B] mt-6 mb-2"&gt;4. Vos droits&lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;p className="text-gray-700 mb-3 text-justify"&gt;</span>
          Vous pouvez à tout moment accéder à vos données, les rectifier ou demander leur suppression en nous contactant à l'adresse <EMAIL>.
<span class="cstat-no" title="statement not covered" >        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;h2 className="text-xl font-semibold text-[#A67B5B] mt-6 mb-2"&gt;5. Cookies&lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;p className="text-gray-700 mb-3 text-justify"&gt;</span>
          Nous utilisons des cookies pour améliorer votre expérience utilisateur. Vous pouvez les désactiver dans les paramètres de votre navigateur.
<span class="cstat-no" title="statement not covered" >        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="mt-8 text-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;span className="text-xs text-gray-400"&gt;Dernière mise à jour : Mai 2025&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default PolitiqueConfidentialite;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-30T11:01:17.113Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    