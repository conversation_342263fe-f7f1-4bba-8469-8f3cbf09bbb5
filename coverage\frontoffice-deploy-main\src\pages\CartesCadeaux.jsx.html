
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for frontoffice-deploy-main/src/pages/CartesCadeaux.jsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">frontoffice-deploy-main/src/pages</a> CartesCadeaux.jsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/45</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/45</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import Chatbot from "@/Components/Chatbot";<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >import React from "react";</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default function CartesCadeaux() {</span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="min-h-screen bg-gradient-to-b from-white to-gray-50 flex flex-col items-center justify-center py-16 px-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="max-w-2xl w-full bg-white rounded-2xl shadow-xl p-10 relative overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;h1 className="text-3xl md:text-4xl font-light text-center mb-6 tracking-widest text-[#A67B5B]"&gt;Cartes cadeaux&lt;/h1&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="w-20 h-1 bg-[#A67B5B] mx-auto mb-8"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;Chatbot /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;p className="text-lg text-gray-700 mb-6 text-center"&gt;</span>
          Offrez le choix et l'élégance avec nos cartes cadeaux Ji-line &amp; Carré Blanc Paris Sfax !
<span class="cstat-no" title="statement not covered" >        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;ul className="mb-8 space-y-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;li className="flex items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;i className="fa-solid fa-gift text-[#A67B5B] text-lg"&gt;&lt;/i&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span className="text-gray-800 font-medium"&gt;Montant au choix (à partir de 150 DT)&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;li className="flex items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;i className="fa-solid fa-envelope-open-text text-[#A67B5B] text-lg"&gt;&lt;/i&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span className="text-gray-800 font-medium"&gt;Carte digitale ou physique&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;li className="flex items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;i className="fa-solid fa-store text-[#A67B5B] text-lg"&gt;&lt;/i&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span className="text-gray-800 font-medium"&gt;Valable en boutique Carré Blanc Paris &amp; J-line Sfax&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;li className="flex items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;i className="fa-solid fa-calendar-check text-[#A67B5B] text-lg"&gt;&lt;/i&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span className="text-gray-800 font-medium"&gt;Valable 12 mois à compter de la date d'achat&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/ul&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;p className="text-center text-gray-500 text-sm mb-4"&gt;</span>
          Pour offrir une carte cadeau ou en savoir plus, rendez-vous en boutique ou contactez notre équipe !
<span class="cstat-no" title="statement not covered" >        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="flex justify-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;img src="/img/gift-card.png" alt="Carte cadeau Jihen-line" className="w-64 rounded-xl shadow-md" onError={e =&gt; e.target.style.display='none'} /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-30T11:01:17.113Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    