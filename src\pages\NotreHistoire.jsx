import Chatbot from "@/Components/Chatbot";
import React from "react";

export default function NotreHistoire() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 flex flex-col items-center justify-center py-16 px-4">
      <div className="max-w-2xl w-full bg-white rounded-2xl shadow-xl p-10 relative overflow-hidden">
        <h1 className="text-3xl md:text-4xl font-light text-center mb-6 tracking-widest text-[#A67B5B]">Notre histoire</h1>
        <div className="w-20 h-1 bg-[#A67B5B] mx-auto mb-8"></div>
        <p className="text-lg text-gray-700 mb-6 text-center">
          Depuis sa création, Ji-line & Carré Blanc Paris Sfax s'est imposé comme une référence dans l'univers de la décoration et du linge de maison haut de gamme à Sfax.
        </p>
         <Chatbot />
        <p className="text-gray-700 mb-6 text-center">
          Notre aventure a débuté avec la passion de proposer des produits d'exception, alliant élégance, qualité et savoir-faire artisanal. Année après année, nous avons élargi notre sélection pour offrir à nos clients les plus grandes marques et les dernières tendances.
        </p>
        <ul className="mb-8 space-y-4">
          <li className="flex items-center">
            <span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
              <i className="fa-solid fa-star text-[#A67B5B] text-lg"></i>
            </span>
            <span className="text-gray-800 font-medium">Excellence et authenticité depuis plus de 4 ans</span>
          </li>
          <li className="flex items-center">
            <span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
              <i className="fa-solid fa-people-group text-[#A67B5B] text-lg"></i>
            </span>
            <span className="text-gray-800 font-medium">Une équipe passionnée à votre service</span>
          </li>
          <li className="flex items-center">
            <span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
              <i className="fa-solid fa-leaf text-[#A67B5B] text-lg"></i>
            </span>
            <span className="text-gray-800 font-medium">Engagement pour la qualité et le respect de l'environnement</span>
          </li>
        </ul>
        <p className="text-center text-gray-500 text-sm mb-4">
          Merci à tous nos clients pour leur confiance et leur fidélité. L'histoire continue avec vous !
        </p>
        <div className="flex justify-center">
          <img src="/img/teamwork.png" alt="Notre équipe" className="w-64 rounded-xl shadow-md" onError={e => e.target.style.display='none'} />
        </div>
      </div>
    </div>
  );
}
