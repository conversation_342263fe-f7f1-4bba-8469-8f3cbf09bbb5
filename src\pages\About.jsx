import Chatbot from "@/Components/Chatbot";
import React from "react";

export default function About() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 py-16 px-4 flex items-center justify-center">
      <div className="max-w-3xl w-full bg-white rounded-2xl shadow-xl p-10 relative overflow-hidden">
        <div className="absolute -top-16 -right-16 w-64 h-64 bg-[#A67B5B]/10 rounded-full z-0"></div>
        <div className="absolute -bottom-16 -left-16 w-64 h-64 bg-[#A67B5B]/10 rounded-full z-0"></div>
        <div className="relative z-10">
          <h1 className="text-4xl font-light text-center mb-6 tracking-widest text-[#A67B5B]">À propos de nous</h1>
          <div className="w-20 h-1 bg-[#A67B5B] mx-auto mb-8"></div>
          <p className="text-lg text-gray-700 mb-6 text-center">
            Bienvenue sur notre showroom en ligne, une vitrine dédiée à l'élégance et au design d'intérieur. Nous vous proposons une sélection raffinée de marques prestigieuses, alliant qualité et style.
          </p>
           <Chatbot />
          <p className="text-gray-700 mb-6 text-center">
            Notre équipe de passionnés sélectionne avec soin chaque article pour vous offrir le meilleur du design et de l'artisanat. Nous croyons que votre intérieur devrait refléter votre personnalité et votre style de vie.
          </p>
          <ul className="mb-8 space-y-3">
            <li className="flex items-center">
              <span className="w-6 h-6 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#A67B5B]" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" /></svg>
              </span>
              Des marques prestigieuses sélectionnées avec soin
            </li>
            <li className="flex items-center">
              <span className="w-6 h-6 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#A67B5B]" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" /></svg>
              </span>
              Un service client attentif et personnalisé
            </li>
            <li className="flex items-center">
              <span className="w-6 h-6 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-[#A67B5B]" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" /></svg>
              </span>
              Des conseils déco pour sublimer votre intérieur
            </li>
          </ul>
          <div className="text-center">
            <img src="/img/interior-moodboard.png" alt="Moodboard" className="mx-auto rounded-xl shadow-md w-full max-w-md" />
          </div>
        </div>
      </div>
    </div>
  );
}
