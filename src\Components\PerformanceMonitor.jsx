import React, { useState, useEffect } from 'react';
import performanceMonitor from '../utils/performanceMonitor';

const PerformanceMonitor = () => {
  const [stats, setStats] = useState(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development mode
    if (process.env.NODE_ENV !== 'development') return;

    const updateStats = () => {
      setStats(performanceMonitor.getStats());
    };

    // Update stats every 5 seconds
    const interval = setInterval(updateStats, 5000);
    updateStats(); // Initial update

    return () => clearInterval(interval);
  }, []);

  // Don't render in production
  if (process.env.NODE_ENV !== 'development' || !stats) return null;

  const recommendations = performanceMonitor.getRecommendations();
  const isHighFrequency = performanceMonitor.isHighFrequency();

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className={`px-3 py-2 rounded-lg text-white text-sm font-medium transition-colors ${
          isHighFrequency 
            ? 'bg-red-500 hover:bg-red-600' 
            : recommendations.length > 0 
              ? 'bg-yellow-500 hover:bg-yellow-600'
              : 'bg-green-500 hover:bg-green-600'
        }`}
      >
        📊 Performance
        {isHighFrequency && ' ⚠️'}
      </button>

      {isVisible && (
        <div className="absolute bottom-12 right-0 bg-white border border-gray-200 rounded-lg shadow-lg p-4 w-80 max-h-96 overflow-y-auto">
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-semibold text-gray-800">Performance Monitor</h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          <div className="space-y-3">
            {/* Cart Stats */}
            <div className="bg-blue-50 p-3 rounded">
              <h4 className="font-medium text-blue-800 mb-2">Cart API Calls</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <div>Total: {stats.cartCalls.total}</div>
                <div>Last minute: {stats.cartCalls.lastMinute}</div>
                <div>Last 5 min: {stats.cartCalls.lastFiveMinutes}</div>
                <div>Avg duration: {stats.cartCalls.averageDuration}ms</div>
              </div>
            </div>

            {/* Wishlist Stats */}
            <div className="bg-purple-50 p-3 rounded">
              <h4 className="font-medium text-purple-800 mb-2">Wishlist API Calls</h4>
              <div className="text-sm text-purple-700 space-y-1">
                <div>Total: {stats.wishlistCalls.total}</div>
                <div>Last minute: {stats.wishlistCalls.lastMinute}</div>
                <div>Last 5 min: {stats.wishlistCalls.lastFiveMinutes}</div>
                <div>Avg duration: {stats.wishlistCalls.averageDuration}ms</div>
              </div>
            </div>

            {/* Overall Stats */}
            <div className="bg-gray-50 p-3 rounded">
              <h4 className="font-medium text-gray-800 mb-2">Overall</h4>
              <div className="text-sm text-gray-700 space-y-1">
                <div>Total requests: {stats.totalRequests}</div>
                <div>Blocked requests: {stats.blockedRequests}</div>
              </div>
            </div>

            {/* Recommendations */}
            {recommendations.length > 0 && (
              <div className="bg-yellow-50 p-3 rounded">
                <h4 className="font-medium text-yellow-800 mb-2">Recommendations</h4>
                <ul className="text-sm text-yellow-700 space-y-1">
                  {recommendations.map((rec, index) => (
                    <li key={index} className="flex items-start">
                      <span className="mr-1">•</span>
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Status Indicator */}
            <div className={`p-3 rounded text-center text-sm font-medium ${
              isHighFrequency 
                ? 'bg-red-100 text-red-800' 
                : recommendations.length > 0 
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-green-100 text-green-800'
            }`}>
              {isHighFrequency 
                ? '⚠️ High API frequency detected'
                : recommendations.length > 0 
                  ? '⚡ Performance can be improved'
                  : '✅ Performance looks good'
              }
            </div>

            {/* Reset Button */}
            <button
              onClick={() => {
                performanceMonitor.reset();
                setStats(performanceMonitor.getStats());
              }}
              className="w-full px-3 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded text-sm transition-colors"
            >
              Reset Stats
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PerformanceMonitor;
