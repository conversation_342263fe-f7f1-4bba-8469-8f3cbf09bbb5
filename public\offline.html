<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hors ligne - Frontoffice</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      background-color: #f9fafb;
      color: #1f2937;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      margin: 0;
      padding: 20px;
      text-align: center;
    }
    
    .container {
      max-width: 500px;
      padding: 40px;
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    
    h1 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #A67B5B;
    }
    
    p {
      font-size: 16px;
      line-height: 1.5;
      margin-bottom: 24px;
      color: #4b5563;
    }
    
    .icon {
      font-size: 48px;
      margin-bottom: 24px;
    }
    
    .button {
      display: inline-block;
      background-color: #A67B5B;
      color: white;
      font-weight: 500;
      padding: 10px 20px;
      border-radius: 6px;
      text-decoration: none;
      transition: background-color 0.2s;
    }
    
    .button:hover {
      background-color: #8B5A2B;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon">📶</div>
    <h1>Vous êtes hors ligne</h1>
    <p>
      Il semble que vous n'ayez pas de connexion Internet.
      Veuillez vérifier votre connexion et réessayer.
    </p>
    <a href="/" class="button" id="retry-button">Réessayer</a>
  </div>
  
  <script>
    // Add event listener to retry button
    document.getElementById('retry-button').addEventListener('click', function(event) {
      event.preventDefault();
      window.location.reload();
    });
    
    // Check if online and redirect if so
    window.addEventListener('online', function() {
      window.location.reload();
    });
  </script>
</body>
</html>
