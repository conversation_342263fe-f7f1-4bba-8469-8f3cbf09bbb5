import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import DynamicButton from './Components/DynamicButton';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';

const renderWithRouter = (ui) => render(<BrowserRouter>{ui}</BrowserRouter>);

describe('DynamicButton', () => {
  it('renders as a button by default', () => {
    renderWithRouter(<DynamicButton label="Click me" />);
    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Click me');
  });

  it('calls onClick when clicked', () => {
    const handleClick = vi.fn();
    renderWithRouter(<DynamicButton label="Click" onClick={handleClick} />);
    const button = screen.getByRole('button');
    fireEvent.click(button);
    expect(handleClick).toHaveBeenCalled();
  });

  it('renders as a link when `to` is provided', () => {
    renderWithRouter(<DynamicButton label="Go Home" to="/home" />);
    const link = screen.getByRole('link', { name: /go home/i });
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', '/home');
  });

  it('is disabled when `disabled` is true', () => {
    renderWithRouter(<DynamicButton label="Disabled" disabled />);
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
  });
});
