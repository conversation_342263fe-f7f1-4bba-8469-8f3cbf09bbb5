
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for frontoffice-deploy-main/src/widgets/layout/footer.jsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">frontoffice-deploy-main/src/widgets/layout</a> footer.jsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/220</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/220</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import React from "react";<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >import PropTypes from "prop-types";</span>
<span class="cstat-no" title="statement not covered" >import { Typography } from "@material-tailwind/react";</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const year = new Date().getFullYear();</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export function Footer({ title, description, copyright }) {</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;footer className="bg-white text-gray-700 pt-12 pb-8"&gt;</span>
      {/* Main Footer Content - Enhanced Design */}
<span class="cstat-no" title="statement not covered" >      &lt;div className="container mx-auto px-6"&gt;</span>
        {/* Footer navigation with improved styling */}
<span class="cstat-no" title="statement not covered" >        &lt;div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10"&gt;</span>
          {/* Column 1 - Client Account */}
<span class="cstat-no" title="statement not covered" >          &lt;div className="transform transition-all duration-500 hover:translate-y-[-5px]"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;Typography variant="h6" className="font-medium mb-5 text-gray-800 relative inline-block"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-12 after:h-0.5 after:bg-[#A67B5B] after:transition-all after:duration-700 group-hover:after:w-full pb-2"&gt;</span>
                Mon Compte
<span class="cstat-no" title="statement not covered" >              &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/Typography&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;ul className="space-y-3 mt-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;li&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;a href="/Profile" className="group flex items-center text-gray-600 hover:text-[#A67B5B] transition-colors duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="mr-2 text-[#A67B5B] transition-transform duration-300 group-hover:translate-x-1"&gt;›&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="transition-colors duration-300"&gt;Mon profil&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;li&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;a href="/commandes" className="group flex items-center text-gray-600 hover:text-[#A67B5B] transition-colors duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="mr-2 text-[#A67B5B] transition-transform duration-300 group-hover:translate-x-1"&gt;›&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="transition-colors duration-300"&gt;Mes commandes&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;li&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;a href="/FavoritesPage" className="group flex items-center text-gray-600 hover:text-[#A67B5B] transition-colors duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="mr-2 text-[#A67B5B] transition-transform duration-300 group-hover:translate-x-1"&gt;›&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="transition-colors duration-300"&gt;Mes favoris&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/li&gt;</span>
             
<span class="cstat-no" title="statement not covered" >            &lt;/ul&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
&nbsp;
          {/* Column 2 - Services */}
<span class="cstat-no" title="statement not covered" >          &lt;div className="transform transition-all duration-500 hover:translate-y-[-5px]"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;Typography variant="h6" className="font-medium mb-5 text-gray-800 relative inline-block"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-12 after:h-0.5 after:bg-[#A67B5B] after:transition-all after:duration-700 group-hover:after:w-full pb-2"&gt;</span>
                Nos Services
<span class="cstat-no" title="statement not covered" >              &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/Typography&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;ul className="space-y-3 mt-6"&gt;</span>
             
<span class="cstat-no" title="statement not covered" >              &lt;li&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;a href="/RetraitEnBoutique" className="group flex items-center text-gray-600 hover:text-[#A67B5B] transition-colors duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="mr-2 text-[#A67B5B] transition-transform duration-300 group-hover:translate-x-1"&gt;›&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="transition-colors duration-300"&gt;Retrait en boutique&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;li&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;a href="/ModesDePaiement" className="group flex items-center text-gray-600 hover:text-[#A67B5B] transition-colors duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="mr-2 text-[#A67B5B] transition-transform duration-300 group-hover:translate-x-1"&gt;›&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="transition-colors duration-300"&gt;Modes de paiement&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/li&gt;</span>
             
<span class="cstat-no" title="statement not covered" >              &lt;li&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;a href="/CartesCadeaux" className="group flex items-center text-gray-600 hover:text-[#A67B5B] transition-colors duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="mr-2 text-[#A67B5B] transition-transform duration-300 group-hover:translate-x-1"&gt;›&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="transition-colors duration-300"&gt;Cartes cadeaux&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/ul&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
&nbsp;
          {/* Column 3 - About */}
<span class="cstat-no" title="statement not covered" >          &lt;div className="transform transition-all duration-500 hover:translate-y-[-5px]"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;Typography variant="h6" className="font-medium mb-5 text-gray-800 relative inline-block"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-12 after:h-0.5 after:bg-[#A67B5B] after:transition-all after:duration-700 group-hover:after:w-full pb-2"&gt;</span>
                À Propos
<span class="cstat-no" title="statement not covered" >              &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/Typography&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;ul className="space-y-3 mt-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;li&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;a href="/marque" className="group flex items-center text-gray-600 hover:text-[#A67B5B] transition-colors duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="mr-2 text-[#A67B5B] transition-transform duration-300 group-hover:translate-x-1"&gt;›&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="transition-colors duration-300"&gt;Nos marques&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;li&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;a href="/NotreHistoire" className="group flex items-center text-gray-600 hover:text-[#A67B5B] transition-colors duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="mr-2 text-[#A67B5B] transition-transform duration-300 group-hover:translate-x-1"&gt;›&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="transition-colors duration-300"&gt;Notre histoire&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;li&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;a href="/ProfessionalPage" className="group flex items-center text-gray-600 hover:text-[#A67B5B] transition-colors duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="mr-2 text-[#A67B5B] transition-transform duration-300 group-hover:translate-x-1"&gt;›&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="transition-colors duration-300"&gt;Espace pro&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;li&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;a href="/DevenirAffilie" className="group flex items-center text-gray-600 hover:text-[#A67B5B] transition-colors duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="mr-2 text-[#A67B5B] transition-transform duration-300 group-hover:translate-x-1"&gt;›&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="transition-colors duration-300"&gt;Devenir partenaire&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/ul&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
&nbsp;
          {/* Column 4 - Contact */}
<span class="cstat-no" title="statement not covered" >          &lt;div className="transform transition-all duration-500 hover:translate-y-[-5px]"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;Typography variant="h6" className="font-medium mb-5 text-gray-800 relative inline-block"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span className="relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-12 after:h-0.5 after:bg-[#A67B5B] after:transition-all after:duration-700 group-hover:after:w-full pb-2"&gt;</span>
                Besoin d'aide ?
<span class="cstat-no" title="statement not covered" >              &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/Typography&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;p className="mb-5 text-gray-600 text-sm leading-relaxed mt-6"&gt;</span>
              Nos conseillers sont à votre disposition pour vous accompagner dans vos choix et répondre à toutes vos questions.
<span class="cstat-no" title="statement not covered" >            &lt;/p&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            &lt;div className="space-y-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;a href="https://wa.me/21612345678" target="_blank" rel="noopener noreferrer" className="flex items-center group p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-all duration-300 hover:shadow-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="w-10 h-10 rounded-full bg-green-50 flex items-center justify-center mr-3 flex-shrink-0 group-hover:bg-green-100 transition-colors duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;i className="fa-brands fa-whatsapp text-green-500 text-lg"&gt;&lt;/i&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="text-sm font-medium text-gray-800"&gt;WhatsApp&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;p className="text-xs text-gray-500 mt-0.5"&gt;Réponse rapide par message&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/a&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              &lt;a href="#" className="flex items-center group p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-all duration-300 hover:shadow-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center mr-3 flex-shrink-0 group-hover:bg-blue-100 transition-colors duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;i className="fa-solid fa-phone text-blue-500 text-lg"&gt;&lt;/i&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="text-sm font-medium text-gray-800"&gt;+216 +216 22 633 815&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;p className="text-xs text-gray-500 mt-0.5"&gt;Lun-sam, 9h-18h&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/a&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              &lt;a href="#" className="flex items-center group p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-all duration-300 hover:shadow-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="w-10 h-10 rounded-full bg-purple-50 flex items-center justify-center mr-3 flex-shrink-0 group-hover:bg-purple-100 transition-colors duration-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;i className="fa-regular fa-envelope text-purple-500 text-lg"&gt;&lt;/i&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;span className="text-sm font-medium text-gray-800"&gt;Email&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;p className="text-xs text-gray-500 mt-0.5"&gt;<EMAIL>&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
&nbsp;
        {/* Social Media Links - Enhanced with modern design */}
<span class="cstat-no" title="statement not covered" >        &lt;div className="mt-16 pt-10 border-t border-gray-100"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex flex-col md:flex-row justify-between items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="mb-8 md:mb-0 transform transition-all duration-500 hover:scale-105"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;a href="/" className="block"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;img</span>
<span class="cstat-no" title="statement not covered" >                  src="/img/logos.png"</span>
<span class="cstat-no" title="statement not covered" >                  alt="Logo"</span>
<span class="cstat-no" title="statement not covered" >                  className="h-28 w-auto max-w-xs md:h-36 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 bg-white p-2 mx-auto"</span>
<span class="cstat-no" title="statement not covered" >                  style={{ objectFit: 'contain' }}</span>
<span class="cstat-no" title="statement not covered" >                /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p className="text-sm text-gray-500 mt-3 max-w-xs"&gt;</span>
                Votre showroom en ligne de marques prestigieuses pour sublimer votre intérieur
<span class="cstat-no" title="statement not covered" >              &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex flex-col items-center md:items-end"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex space-x-4 mb-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;a</span>
<span class="cstat-no" title="statement not covered" >                  href="https://www.facebook.com/CarreBlancParisSfax?locale=fr_FR"</span>
<span class="cstat-no" title="statement not covered" >                  className="text-gray-700 hover:text-blue-600 transition-all duration-300 w-12 h-12 flex items-center justify-center rounded-full border border-gray-200 hover:border-blue-200 hover:bg-blue-50 shadow-sm hover:shadow-md hover:scale-110"</span>
<span class="cstat-no" title="statement not covered" >                  aria-label="Facebook"</span>
                &gt;
<span class="cstat-no" title="statement not covered" >                  &lt;i className="fa-brands fa-facebook-f text-xl"&gt;&lt;/i&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;a</span>
<span class="cstat-no" title="statement not covered" >                  href="https://www.instagram.com/carre_blanc_jline_sfax/"</span>
<span class="cstat-no" title="statement not covered" >                  className="text-gray-700 hover:text-pink-600 transition-all duration-300 w-12 h-12 flex items-center justify-center rounded-full border border-gray-200 hover:border-pink-200 hover:bg-pink-50 shadow-sm hover:shadow-md hover:scale-110"</span>
<span class="cstat-no" title="statement not covered" >                  aria-label="Instagram"</span>
                &gt;
<span class="cstat-no" title="statement not covered" >                  &lt;i className="fa-brands fa-instagram text-xl"&gt;&lt;/i&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/a&gt;</span>
               
                
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              &lt;div className="text-sm text-gray-600 text-center md:text-right"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="mb-1 font-medium"&gt;© {year} Ji-line&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-xs text-gray-500"&gt;Tous droits réservés | Showroom en ligne de marques prestigieuses&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
&nbsp;
        {/* Bottom Service Highlights - Enhanced with modern design */}
<span class="cstat-no" title="statement not covered" >        &lt;div className="mt-12 pt-10 border-t border-gray-100"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="grid grid-cols-1 sm:grid-cols-3 gap-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center p-6 bg-gray-50 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-500 hover:bg-white group transform hover:scale-105"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="w-14 h-14 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-5 flex-shrink-0 group-hover:bg-[#A67B5B]/20 transition-all duration-300 group-hover:scale-110"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;i className="fa-solid fa-lock text-[#A67B5B] text-xl"&gt;&lt;/i&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h4 className="font-medium text-gray-800 mb-1"&gt;Paiement sécurisé&lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-xs text-gray-500 leading-relaxed"&gt;Transactions cryptées et sécurisées pour vos achats en ligne&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center p-6 bg-gray-50 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-500 hover:bg-white group transform hover:scale-105"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="w-14 h-14 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-5 flex-shrink-0 group-hover:bg-[#A67B5B]/20 transition-all duration-300 group-hover:scale-110"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;i className="fa-solid fa-truck text-[#A67B5B] text-xl"&gt;&lt;/i&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h4 className="font-medium text-gray-800 mb-1"&gt;Livraison offerte&lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-xs text-gray-500 leading-relaxed"&gt;En boutique ou à domicile pour toute commande supérieure à 150DT&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center p-6 bg-gray-50 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-500 hover:bg-white group transform hover:scale-105"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="w-14 h-14 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-5 flex-shrink-0 group-hover:bg-[#A67B5B]/20 transition-all duration-300 group-hover:scale-110"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;i className="fa-solid fa-headset text-[#A67B5B] text-xl"&gt;&lt;/i&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h4 className="font-medium text-gray-800 mb-1"&gt;Service client&lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-xs text-gray-500 leading-relaxed"&gt;Assistance personnalisée 6j/7 de 9h à 18h pour vous accompagner&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
&nbsp;
          {/* Legal links - Enhanced */}
<span class="cstat-no" title="statement not covered" >          &lt;div className="mt-12 pt-8 border-t border-gray-100 flex flex-wrap justify-center gap-x-10 gap-y-3 text-xs text-gray-500"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;a href="/ConditionsGenerales" className="hover:text-[#A67B5B] transition-all duration-300 hover:underline"&gt;Conditions générales&lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;a href="/PolitiqueConfidentialite" className="hover:text-[#A67B5B] transition-all duration-300 hover:underline"&gt;Politique de confidentialité&lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;a href="/MentionsLegales" className="hover:text-[#A67B5B] transition-all duration-300 hover:underline"&gt;Mentions légales&lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;a href="/PlanDuSite" className="hover:text-[#A67B5B] transition-all duration-300 hover:underline"&gt;Plan du site&lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;a href="/FAQ" className="hover:text-[#A67B5B] transition-all duration-300 hover:underline"&gt;FAQ&lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/footer&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >Footer.defaultProps = {</span>
<span class="cstat-no" title="statement not covered" >  title: "Jihen-line",</span>
<span class="cstat-no" title="statement not covered" >  description: "Showroom en ligne de marques prestigieuses",</span>
<span class="cstat-no" title="statement not covered" >  copyright: `Copyright © ${year} Jihen-line. Tous droits réservés.`,</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >Footer.propTypes = {</span>
<span class="cstat-no" title="statement not covered" >  title: PropTypes.string,</span>
<span class="cstat-no" title="statement not covered" >  description: PropTypes.string,</span>
<span class="cstat-no" title="statement not covered" >  copyright: PropTypes.node,</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >Footer.displayName = "/src/widgets/layout/footer.jsx";</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default Footer;</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-30T11:01:17.113Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    