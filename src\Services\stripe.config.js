import { loadStripe } from '@stripe/stripe-js';

// Stripe configuration
export const STRIPE_CONFIG = {
  // Stripe publishable key from the guide
  publishableKey: 'pk_test_51RTgI3D1WIE8Jd79dqtCV1q2iaMc1sAaJL6inuoAFCuvOUZoUePpIEX4Lctc9tAx70KS8Nsl23HuPzse52Y38s5y00chgUqWoN',

  // Appearance configuration for Stripe Elements
  appearance: {
    theme: 'stripe',
    variables: {
      colorPrimary: '#A67B5B',
      colorBackground: '#ffffff',
      colorText: '#374151',
      colorDanger: '#ef4444',
      fontFamily: 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto',
      borderRadius: '6px',
      spacingUnit: '4px'
    },
    rules: {
      '.Input': {
        border: '1px solid #d1d5db',
        boxShadow: 'none',
        padding: '12px',
        fontSize: '16px'
      },
      '.Input:focus': {
        border: '1px solid #A67B5B',
        boxShadow: '0 0 0 2px rgba(166, 123, 91, 0.1)'
      },
      '.Input--invalid': {
        border: '1px solid #ef4444'
      },
      '.Label': {
        fontSize: '14px',
        fontWeight: '500',
        color: '#374151',
        marginBottom: '4px'
      }
    }
  },

  // Payment element options
  paymentElementOptions: {
    layout: 'tabs',
    defaultValues: {
      billingDetails: {
        address: {
          country: 'TN' // Tunisia
        }
      }
    }
  }
};

// Available currencies
export const SUPPORTED_CURRENCIES = [
  { code: 'eur', symbol: '€', name: 'Euro' },
  { code: 'usd', symbol: '$', name: 'US Dollar' },
  { code: 'tnd', symbol: 'DT', name: 'Tunisian Dinar' }
];

// Payment method types
export const PAYMENT_METHODS = {
  CARD: 'card',
  CASH_ON_DELIVERY: 'cash_on_delivery',
  LOCAL_PICKUP: 'local_pickup',
  STRIPE: 'stripe'
};

// Payment status constants
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  SUCCEEDED: 'succeeded',
  FAILED: 'failed',
  REQUIRES_ACTION: 'requires_action',
  REQUIRES_PAYMENT_METHOD: 'requires_payment_method',  CANCELED: 'canceled'
};

// Initialize Stripe
export const stripePromise = loadStripe(STRIPE_CONFIG.publishableKey);

export default STRIPE_CONFIG;
