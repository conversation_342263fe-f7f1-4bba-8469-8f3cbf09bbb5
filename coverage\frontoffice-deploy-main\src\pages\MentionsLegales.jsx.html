
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for frontoffice-deploy-main/src/pages/MentionsLegales.jsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">frontoffice-deploy-main/src/pages</a> MentionsLegales.jsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/23</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/23</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import Chatbot from "@/Components/Chatbot";<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >import React from "react";</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const MentionsLegales = () =&gt; (</span>
<span class="cstat-no" title="statement not covered" >  &lt;div className="min-h-screen bg-gradient-to-br from-[#f7f3ef] to-[#fff] py-12 px-4 sm:px-8 md:px-20 lg:px-48"&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="max-w-3xl mx-auto bg-white rounded-2xl shadow-xl p-8 md:p-12"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;h1 className="text-3xl md:text-4xl font-bold text-[#A67B5B] mb-6 text-center"&gt;Mentions légales&lt;/h1&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;p className="text-gray-700 mb-4 text-justify"&gt;Conformément à la législation, retrouvez ci-dessous les informations légales concernant Ji-line &amp; Carré Blanc Paris Sfax.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ul className="text-gray-700 mb-4 text-justify list-disc pl-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;strong&gt;Éditeur du site :&lt;/strong&gt; Ji-line &amp; Carré Blanc Paris Sfax&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;strong&gt;Adresse :&lt;/strong&gt; Avenue Majida Boulila, Sfax, Tunisie&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;strong&gt;Téléphone :&lt;/strong&gt; +216 22 633 815&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;strong&gt;Email :&lt;/strong&gt; <EMAIL>&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;strong&gt;Directeur de la publication :&lt;/strong&gt; Jihen-line&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;strong&gt;Hébergement :&lt;/strong&gt; OVH, 2 rue Kellermann, 59100 Roubaix, France&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/ul&gt;</span>
<span class="cstat-no" title="statement not covered" >       &lt;Chatbot /&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;p className="text-gray-700 mb-4 text-justify"&gt;Toute reproduction ou représentation totale ou partielle de ce site est interdite sans autorisation écrite préalable.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="mt-8 text-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;span className="text-xs text-gray-400"&gt;Dernière mise à jour : Mai 2025&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >  &lt;/div&gt;</span>
);
&nbsp;
<span class="cstat-no" title="statement not covered" >export default MentionsLegales;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-30T11:01:17.113Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    