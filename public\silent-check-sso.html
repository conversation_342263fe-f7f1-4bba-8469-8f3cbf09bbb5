<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>Silent SSO Check</title>
</head>

<body>
    <script>
        // Use a try-catch to handle potential CSP issues
        try {
            parent.postMessage(location.href, location.origin);
        } catch (e) {
            console.error('Silent check SSO error:', e);
            // Try with wildcard origin as fallback
            try {
                parent.postMessage(location.href, '*');
            } catch (e2) {
                console.error('Silent check SSO fallback error:', e2);
            }
        }
    </script>
</body>

</html>