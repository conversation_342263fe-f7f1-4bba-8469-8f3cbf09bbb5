/* Custom Carousel Styling */

/* Main carousel container */
.hero-carousel {
  position: relative;
  margin-bottom: 0 !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  height: 550px !important; /* Reduced fixed height */
  line-height: 0; /* Eliminate extra space */
}

/* Slide container */
.hero-slide {
  position: relative;
  overflow: hidden;
  border-radius: 0;
  height: 550px !important; /* Reduced fixed height */
  line-height: 0; /* Eliminate extra space */
}

/* Slide image */
.hero-slide img {
  width: 100%;
  height: 100% !important; /* Force full height */
  object-fit: cover !important; /* Ensure image covers the area */
  object-position: center;
  transition: transform 0.7s ease-in-out;
}

/* Ensure the carousel maintains fixed height */
.hero-carousel .slick-list,
.hero-carousel .slick-track,
.hero-carousel .slick-slide,
.hero-carousel .slick-slide > div {
  height: 550px !important;
}

/* Fixed height for loading, error, and empty states */
.hero-carousel.bg-gray-100 {
  height: 550px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Slide content overlay */
.hero-slide-content {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.6));
  color: white;
  z-index: 10;
}

/* Slide title */
.hero-slide-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Slide description */
.hero-slide-desc {
  font-size: 1.5rem;
  max-width: 800px;
  margin-bottom: 2rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition-delay: 0.1s;
}

/* Slide button */
.hero-slide-btn {
  padding: 0.75rem 1.5rem;
  background-color: transparent;
  border: 2px solid white;
  color: white;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition-delay: 0.2s;
}

.hero-slide-btn:hover {
  background-color: #A67B5B;
  border-color: #A67B5B;
  transform: translateY(-2px);
}

/* Active slide animations */
.hero-slide.active .hero-slide-title,
.hero-slide.active .hero-slide-desc,
.hero-slide.active .hero-slide-btn {
  transform: translateY(0);
  opacity: 1;
}

/* Slide image zoom effect on active */
.hero-slide.active img {
  transform: scale(1.05);
}

/* Custom dots styling */
.hero-carousel .slick-dots {
  bottom: 20px;
  z-index: 20;
  background-color: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(4px);
  border-radius: 30px;
  padding: 8px 16px;
  display: inline-flex !important;
  width: auto !important;
  left: 50%;
  transform: translateX(-50%);
}

.hero-carousel .slick-dots li {
  margin: 0 5px;
  width: 12px;
  height: 12px;
}

.hero-carousel .slick-dots li button {
  width: 12px;
  height: 12px;
  padding: 0;
}

.hero-carousel .slick-dots li button:before {
  font-size: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.7);
  opacity: 0.7;
  content: '';
  transition: all 0.3s ease;
}

.hero-carousel .slick-dots li.slick-active button:before {
  background-color: #A67B5B;
  opacity: 1;
  transform: scale(1.2);
}

.hero-carousel .slick-dots li button:hover:before {
  opacity: 1;
  background-color: rgba(166, 123, 91, 0.5);
}

/* Custom arrows styling */
.hero-carousel .slick-prev,
.hero-carousel .slick-next {
  width: 40px;
  height: 40px;
  z-index: 20;
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.hero-carousel .slick-prev {
  left: 20px;
}

.hero-carousel .slick-next {
  right: 20px;
}

.hero-carousel .slick-prev:hover,
.hero-carousel .slick-next:hover {
  background-color: rgba(166, 123, 91, 0.8);
}

.hero-carousel .slick-prev:before,
.hero-carousel .slick-next:before {
  font-size: 20px;
  opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-slide-title {
    font-size: 2rem;
  }

  .hero-slide-desc {
    font-size: 1rem;
  }

  .hero-carousel .slick-prev {
    left: 10px;
  }

  .hero-carousel .slick-next {
    right: 10px;
  }
}

/* Remove extra space below carousel */
.slick-slider {
  margin-bottom: 0 !important;
  line-height: 0;
}

.slick-list {
  margin-bottom: 0 !important;
}

.carousel-container {
  line-height: 0;
  font-size: 0;
}

.hero-slide-content {
  line-height: normal;
  font-size: 16px;
}

/* Gradient overlay at the bottom of the carousel */
.carousel-bottom-gradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
  z-index: 5;
  pointer-events: none;
}

/* Fix for extra space below carousel */
.carousel-container {
  margin-bottom: 0 !important;
  font-size: 0;
  line-height: 0;
}
