name: material-tailwind-kit-react
region: us-east-1
frontend:
  # Specifies the path of your code.
  path: .
  # Specifies the folder where the build is located.
  # This is the folder that will be deployed.
  publish: dist
  # Scripts will run in the specified `path` folder.
  scripts:
    # The command to build your frontend project. This is custom to your project.
    # It must to populate the specified `publish` folder with a `index.html` file.
    deploy:
      - npm install --legacy-peer-deps
      - npm run build
yamlVersion: 2