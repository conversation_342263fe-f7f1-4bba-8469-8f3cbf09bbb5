import {
  ChatBubbleBottomCenterTextIcon,
} from "@heroicons/react/24/solid";

export const featuresData = [
  {
    color: "gray",
    title: "Awarded Agency",
    icon: ChatBubbleBottomCenterTextIcon,
    description:
      "Divide details about your product or agency work into parts. A paragraph describing a feature will be enough.",
  },
  {
    color: "gray",
    title: "Free Revisions",
    icon: ChatBubbleBottomCenterTextIcon,
    description:
      "Keep you user engaged by providing meaningful information. Remember that by this time, the user is curious.",
  },
  {
    color: "gray",
    title: "Verified Company",
    icon: ChatB<PERSON>bleBottomCenterTextIcon,
    description:
      "Write a few lines about each one. A paragraph describing a feature will be enough. Keep you user engaged!",
  },
];

export default featuresData;
