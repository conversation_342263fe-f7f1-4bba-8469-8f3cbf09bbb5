import { render, fireEvent } from "@testing-library/react";
import '@testing-library/jest-dom';
import ToggleText from "./ToggleText";

test("le texte s'affiche et se cache au clic", () => {
  const { getByText, queryByText } = render(<ToggleText />);
  const button = getByText(/Afficher le texte/i);
  // Le texte ne doit pas être visible au départ
  expect(queryByText(/Bonjour, ceci est un texte caché/i)).toBeNull();
  // Cliquer pour afficher
  fireEvent.click(button);
  expect(getByText(/Bonjour, ceci est un texte caché/i)).toBeInTheDocument();
  // Cliquer pour cacher
  fireEvent.click(button);
  expect(queryByText(/Bonjour, ceci est un texte caché/i)).toBeNull();
});
