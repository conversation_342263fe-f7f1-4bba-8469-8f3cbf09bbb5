import axios from 'axios';
import { keycloak } from './keycloakInstance';

const API_URL = 'https://laravel-api.fly.dev/api';

// Create a custom axios instance for payment operations
const paymentAxios = axios.create({
  baseURL: API_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Add request interceptor to include authentication token
paymentAxios.interceptors.request.use(
  config => {
    // Add authentication token if available
    if (keycloak && keycloak.authenticated && keycloak.token) {
      config.headers.Authorization = `Bearer ${keycloak.token}`;
    } else {
      // Try to get user from localStorage as fallback
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const user = JSON.parse(userStr);
          if (user && user.token) {
            config.headers.Authorization = `Bearer ${user.token}`;
          }
        }
      } catch (error) {
        // Silently handle error
      }
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

class PaymentService {  /**
   * Create a Stripe Payment Intent
   *
   * @param {Object} paymentData - Payment data including commande_id (required)
   * @returns {Promise} - Promise with payment intent data
   */async createPaymentIntent(paymentData) {
    try {      // Validate required fields
      if (!paymentData.amount) {
        return {
          status: 'error',
          message: 'Le montant est requis pour créer un paiement.',
          error: 'Amount is required'
        };
      }

      if (!paymentData.commande_id) {
        return {
          status: 'error',
          message: 'L\'ID de commande est requis pour créer un paiement.',
          error: 'commande_id is required'
        };
      }

      // Ensure token is fresh before making the request
      if (keycloak && keycloak.authenticated) {
        try {
          const refreshed = await keycloak.updateToken(30);
          if (refreshed) {
            // Update the Authorization header with the fresh token
            paymentAxios.defaults.headers.common.Authorization = `Bearer ${keycloak.token}`;
          }
        } catch (refreshError) {
          // Silently handle error
        }
      }      // Format the payment data according to backend expectations
      const formattedPaymentData = {
        amount: parseFloat(paymentData.amount),
        currency: paymentData.currency || 'eur',
        customer_email: paymentData.customer_email,
        shipping_address: paymentData.shipping_address,
        metadata: paymentData.metadata || {}
      };

      // Include original currency information for TND to EUR conversion
      // Backend expects these fields for proper currency conversion
      if (paymentData.original_currency) {
        formattedPaymentData.original_currency = paymentData.original_currency;
      }
      if (paymentData.original_amount) {
        formattedPaymentData.original_amount = paymentData.original_amount;
      }

      // Include commande_id - it's now required by the API
      if (paymentData.commande_id) {
        formattedPaymentData.commande_id = paymentData.commande_id;
      } else {
        return {
          status: 'error',
          message: 'L\'ID de commande est requis pour créer un paiement.',
          error: 'commande_id is required'
        };
      }

      const response = await paymentAxios.post('/stripe/create-payment-intent', formattedPaymentData);      // Check if the response indicates success
      if (response.data && response.data.success) {
        const result = {
          status: 'success',
          client_secret: response.data.client_secret,
          payment_intent_id: response.data.payment_intent_id,
          amount: response.data.amount,
          currency: response.data.currency,
          paiement_id: response.data.paiement_id
        };

        // Include currency conversion data if present
        if (response.data.conversion_data) {
          result.conversion_data = response.data.conversion_data;
        }

        return result;
      } else {
        return {
          status: 'error',
          message: response.data.error || 'Erreur lors de la création du paiement',
          error: response.data.error || 'Payment intent creation failed'
        };
      }

    } catch (error) {
      // Handle error
      if (error.response) {
        if (error.response.status === 422) {
          // Validation errors
          return {
            status: 'error',
            message: 'Erreur de validation des données de paiement',
            errors: error.response.data.errors,
            error: 'Validation failed'
          };
        } else if (error.response.status === 401) {
          return {
            status: 'error',
            message: 'Vous devez être connecté pour effectuer un paiement.',
            error: 'Authentication required'
          };
        } else {
          return {
            status: 'error',
            message: error.response.data.error || 'Erreur lors de la création du paiement',
            error: error.response.data.error || 'Payment creation failed'
          };
        }
      } else if (error.request) {
        return {
          status: 'error',
          message: 'Erreur de connexion. Veuillez vérifier votre connexion internet.',
          error: 'Network error'
        };
      } else {
        return {
          status: 'error',
          message: 'Une erreur inattendue s\'est produite.',
          error: error.message
        };
      }
    }
  }

  /**
   * Confirm a Stripe Payment
   *
     /**
   * Confirm a Stripe Payment
   *
   * @param {Object} confirmData - Payment confirmation data
   * @returns {Promise} - Promise with payment confirmation result
   */
  async confirmPayment(confirmData) {
    try {
      // Validate required fields
      if (!confirmData.payment_intent_id) {
        return {
          status: 'error',
          message: 'L\'ID de l\'intention de paiement est requis.',
          error: 'Payment intent ID is required'
        };
      }

      if (!confirmData.payment_method_id) {
        return {
          status: 'error',
          message: 'La méthode de paiement est requise.',
          error: 'Payment method ID is required'
        };
      }

      // Ensure token is fresh before making the request
      if (keycloak && keycloak.authenticated) {
        try {
          const refreshed = await keycloak.updateToken(30);
          if (refreshed) {
            paymentAxios.defaults.headers.common.Authorization = `Bearer ${keycloak.token}`;
          }
        } catch (refreshError) {
          // Silently handle error
        }
      }

      const response = await paymentAxios.post('/stripe/confirm-payment', confirmData);

      if (response.data && response.data.success) {
        return {
          status: 'success',
          payment_status: response.data.status,
          payment_intent: response.data.payment_intent,
          requires_action: response.data.requires_action,
          client_secret: response.data.client_secret
        };
      } else {
        return {
          status: 'error',
          message: response.data.error || 'Erreur lors de la confirmation du paiement',
          error: response.data.error || 'Payment confirmation failed'
        };
      }

    } catch (error) {
      if (error.response) {
        return {
          status: 'error',
          message: error.response.data.error || 'Erreur lors de la confirmation du paiement',
          error: error.response.data.error || 'Payment confirmation failed'
        };
      } else {
        return {
          status: 'error',
          message: 'Erreur de connexion lors de la confirmation du paiement.',
          error: 'Network error'
        };
      }
    }
  }

  /**
   * Get Payment Intent status
   *
   * @param {string} paymentIntentId - Payment Intent ID
   * @returns {Promise} - Promise with payment intent status
   */
  async getPaymentIntentStatus(paymentIntentId) {
    try {
      if (!paymentIntentId) {
        return {
          status: 'error',
          message: 'L\'ID de l\'intention de paiement est requis.',
          error: 'Payment intent ID is required'
        };
      }

      // Ensure token is fresh before making the request
      if (keycloak && keycloak.authenticated) {
        try {
          const refreshed = await keycloak.updateToken(30);
          if (refreshed) {
            paymentAxios.defaults.headers.common.Authorization = `Bearer ${keycloak.token}`;
          }
        } catch (refreshError) {
          // Silently handle error
        }
      }

      const response = await paymentAxios.get(`/stripe/payment-intent/${paymentIntentId}`);

      if (response.data && response.data.success) {
        return {
          status: 'success',
          payment_intent: response.data.payment_intent,
          local_payment: response.data.local_payment
        };
      } else {
        return {
          status: 'error',
          message: response.data.error || 'Erreur lors de la récupération du statut du paiement',
          error: response.data.error || 'Failed to get payment intent status'
        };
      }

    } catch (error) {
      if (error.response) {
        if (error.response.status === 404) {
          return {
            status: 'error',
            message: 'Intention de paiement non trouvée.',
            error: 'Payment intent not found'
          };
        } else {
          return {
            status: 'error',
            message: error.response.data.error || 'Erreur lors de la récupération du statut du paiement',
            error: error.response.data.error || 'Failed to get payment intent status'
          };
        }
      } else {
        return {
          status: 'error',
          message: 'Erreur de connexion lors de la récupération du statut du paiement.',
          error: 'Network error'
        };
      }
    }
  }
}

// Create a singleton instance
const paymentService = new PaymentService();

export default paymentService;
