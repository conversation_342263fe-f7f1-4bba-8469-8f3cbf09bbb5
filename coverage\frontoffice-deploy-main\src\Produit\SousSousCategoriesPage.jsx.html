
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for frontoffice-deploy-main/src/Produit/SousSousCategoriesPage.jsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">frontoffice-deploy-main/src/Produit</a> SousSousCategoriesPage.jsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/132</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/132</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import { useState, useEffect } from "react";<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >import { useParams, useNavigate } from "react-router-dom";</span>
<span class="cstat-no" title="statement not covered" >import DynamicButton from "../Components/DynamicButton";</span>
<span class="cstat-no" title="statement not covered" >import Chatbot from "@/Components/Chatbot";</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const SousSousCategoriesPage = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const { id } = useParams(); // ✅ Récupère l'ID de la sous-catégorie depuis l'URL</span>
<span class="cstat-no" title="statement not covered" >  const [subSubCategories, setSubSubCategories] = useState([]);</span>
<span class="cstat-no" title="statement not covered" >  const [categoryName, setCategoryName] = useState(""); // Nouveau state pour le nom de la catégorie</span>
<span class="cstat-no" title="statement not covered" >  const [loading, setLoading] = useState(true);</span>
<span class="cstat-no" title="statement not covered" >  const [error, setError] = useState(null);</span>
<span class="cstat-no" title="statement not covered" >  const [currentPage, setCurrentPage] = useState(1);</span>
<span class="cstat-no" title="statement not covered" >  const itemsPerPage = 8; // Nombre d'éléments par page</span>
<span class="cstat-no" title="statement not covered" >  const navigate = useNavigate(); // Utilisation de useNavigate pour la redirection</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (!id) {</span>
<span class="cstat-no" title="statement not covered" >      setError("ID de sous-catégorie invalide.");</span>
<span class="cstat-no" title="statement not covered" >      setLoading(false);</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
    // Récupérer les sous-sous-catégories de cette sous-catégorie
<span class="cstat-no" title="statement not covered" >    fetch(`https://laravel-api.fly.dev/api/sous_sousCategories?sous_categorie_id=${id}`)</span>
<span class="cstat-no" title="statement not covered" >      .then(res =&gt; res.json())</span>
<span class="cstat-no" title="statement not covered" >      .then(data =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const filteredSubSubCategories = data.filter(sub =&gt; sub.sous_categorie_id == id);</span>
<span class="cstat-no" title="statement not covered" >        setSubSubCategories(filteredSubSubCategories);</span>
<span class="cstat-no" title="statement not covered" >      })</span>
<span class="cstat-no" title="statement not covered" >      .catch(() =&gt; setError("Erreur lors du chargement des sous-sous-catégories."))</span>
<span class="cstat-no" title="statement not covered" >      .finally(() =&gt; setLoading(false));</span>
&nbsp;
    // Récupérer le nom de la catégorie en fonction de l'ID
<span class="cstat-no" title="statement not covered" >    fetch(`https://laravel-api.fly.dev/api/sousCategories/${id}`)</span>
<span class="cstat-no" title="statement not covered" >      .then(res =&gt; res.json())</span>
<span class="cstat-no" title="statement not covered" >      .then(data =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (data &amp;&amp; data.nom_sous_categorie) {</span>
<span class="cstat-no" title="statement not covered" >          setCategoryName(data.nom_sous_categorie);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      })</span>
<span class="cstat-no" title="statement not covered" >      .catch(() =&gt; setError("Erreur lors du chargement du nom de la catégorie."));</span>
<span class="cstat-no" title="statement not covered" >  }, [id]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (loading) return &lt;div className="text-center mt-10"&gt;Chargement...&lt;/div&gt;;</span>
<span class="cstat-no" title="statement not covered" >  if (error) return &lt;div className="text-center mt-10 text-red-500"&gt;{error}&lt;/div&gt;;</span>
&nbsp;
  // Gestion de la pagination
<span class="cstat-no" title="statement not covered" >  const indexOfLastItem = currentPage * itemsPerPage;</span>
<span class="cstat-no" title="statement not covered" >  const indexOfFirstItem = indexOfLastItem - itemsPerPage;</span>
<span class="cstat-no" title="statement not covered" >  const currentItems = subSubCategories.slice(indexOfFirstItem, indexOfLastItem);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleClick = (subSubCategoryId) =&gt; {</span>
    // Redirection vers la page des articles de cette sous-sous-catégorie
<span class="cstat-no" title="statement not covered" >    navigate(`/articles/${subSubCategoryId}`);</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="min-h-screen bg-gray-50 text-gray-800 font-serif"&gt;</span>
<span class="cstat-no" title="statement not covered" >       &lt;Chatbot /&gt;</span>
      {/* En-tête minimaliste et élégant */}
<span class="cstat-no" title="statement not covered" >      &lt;div className="py-20 bg-white"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="container mx-auto px-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="text-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;p className="text-xs uppercase tracking-[0.3em] text-gray-400 mb-3"&gt;Collection&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;h1 className="text-4xl font-extralight tracking-[0.15em] mb-4 text-gray-900"&gt;</span>
<span class="cstat-no" title="statement not covered" >              {categoryName ? categoryName.toUpperCase() : "COLLECTIONS"}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/h1&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="w-16 h-[0.5px] bg-[#A67B5B] mx-auto my-6 opacity-60"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;p className="text-gray-500 font-light text-sm tracking-wide max-w-xl mx-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >              Découvrez notre sélection de &lt;span className="text-[#A67B5B] font-normal"&gt;{subSubCategories.length}&lt;/span&gt; produits exclusifs, soigneusement choisis pour leur élégance</span>
<span class="cstat-no" title="statement not covered" >            &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
&nbsp;
      {/* Liste des sous-sous-catégories avec pagination */}
<span class="cstat-no" title="statement not covered" >      &lt;div className="container mx-auto px-6 py-16"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {currentItems.map((subSubCategory) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >            &lt;div</span>
<span class="cstat-no" title="statement not covered" >              key={subSubCategory.id}</span>
<span class="cstat-no" title="statement not covered" >              className="group bg-white overflow-hidden cursor-pointer transition-all duration-500 hover:shadow-[0_8px_30px_rgba(0,0,0,0.04)]"</span>
<span class="cstat-no" title="statement not covered" >              onClick={() =&gt; handleClick(subSubCategory.id)}</span>
            &gt;
<span class="cstat-no" title="statement not covered" >              &lt;div className="relative overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;img</span>
<span class="cstat-no" title="statement not covered" >                  src={subSubCategory.image}</span>
<span class="cstat-no" title="statement not covered" >                  alt={subSubCategory.nom_sous_sous_categorie}</span>
<span class="cstat-no" title="statement not covered" >                  className="w-full h-64 object-cover transition-transform duration-700 group-hover:scale-105"</span>
<span class="cstat-no" title="statement not covered" >                /&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-10 transition-opacity duration-500"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="absolute bottom-0 left-0 w-full h-16 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="p-5 relative"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h3 className="text-base font-light tracking-wide mb-2 text-gray-800 group-hover:text-[#A67B5B] transition-colors"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  {subSubCategory.nom_sous_sous_categorie}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="w-8 h-[0.5px] bg-[#A67B5B] mb-3 transition-all duration-300 group-hover:w-16 opacity-60"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-gray-500 text-sm font-light leading-relaxed"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  {subSubCategory.description_sous_sous_categorie || "Découvrez notre collection exclusive"}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          ))}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
&nbsp;
        {/* Pagination minimaliste */}
<span class="cstat-no" title="statement not covered" >        {subSubCategories.length &gt; itemsPerPage &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex justify-center mt-16"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="inline-flex items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button</span>
<span class="cstat-no" title="statement not covered" >                className={`w-8 h-8 flex items-center justify-center text-xs ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-[#A67B5B]'}`}</span>
<span class="cstat-no" title="statement not covered" >                onClick={() =&gt; currentPage &gt; 1 &amp;&amp; setCurrentPage(currentPage - 1)}</span>
<span class="cstat-no" title="statement not covered" >                disabled={currentPage === 1}</span>
              &gt;
<span class="cstat-no" title="statement not covered" >                &lt;svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 19l-7-7 7-7" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              {Array.from({ length: Math.ceil(subSubCategories.length / itemsPerPage) }, (_, i) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                &lt;button</span>
<span class="cstat-no" title="statement not covered" >                  key={i}</span>
<span class="cstat-no" title="statement not covered" >                  className={`w-8 h-8 flex items-center justify-center text-xs transition-colors ${</span>
<span class="cstat-no" title="statement not covered" >                    currentPage === i + 1</span>
<span class="cstat-no" title="statement not covered" >                      ? "text-[#A67B5B] border-b border-[#A67B5B]"</span>
<span class="cstat-no" title="statement not covered" >                      : "text-gray-500 hover:text-[#A67B5B]"</span>
<span class="cstat-no" title="statement not covered" >                  }`}</span>
<span class="cstat-no" title="statement not covered" >                  onClick={() =&gt; setCurrentPage(i + 1)}</span>
                &gt;
<span class="cstat-no" title="statement not covered" >                  {i + 1}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              ))}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >              &lt;button</span>
<span class="cstat-no" title="statement not covered" >                className={`w-8 h-8 flex items-center justify-center text-xs ${</span>
<span class="cstat-no" title="statement not covered" >                  currentPage === Math.ceil(subSubCategories.length / itemsPerPage)</span>
<span class="cstat-no" title="statement not covered" >                    ? 'text-gray-300 cursor-not-allowed'</span>
<span class="cstat-no" title="statement not covered" >                    : 'text-gray-500 hover:text-[#A67B5B]'</span>
<span class="cstat-no" title="statement not covered" >                }`}</span>
<span class="cstat-no" title="statement not covered" >                onClick={() =&gt;</span>
<span class="cstat-no" title="statement not covered" >                  currentPage &lt; Math.ceil(subSubCategories.length / itemsPerPage) &amp;&amp;</span>
<span class="cstat-no" title="statement not covered" >                  setCurrentPage(currentPage + 1)</span>
                }
<span class="cstat-no" title="statement not covered" >                disabled={currentPage === Math.ceil(subSubCategories.length / itemsPerPage)}</span>
              &gt;
<span class="cstat-no" title="statement not covered" >                &lt;svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5l7 7-7 7" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
        )}
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default SousSousCategoriesPage;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-30T11:01:17.113Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    