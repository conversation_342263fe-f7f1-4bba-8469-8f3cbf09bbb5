import Chatbot from "@/Components/Chatbot";
import React from "react";

const PlanDuSite = () => (
  <div className="min-h-screen bg-gradient-to-br from-[#f7f3ef] to-[#fff] py-12 px-4 sm:px-8 md:px-20 lg:px-48">
    <div className="max-w-3xl mx-auto bg-white rounded-2xl shadow-xl p-8 md:p-12">
      <h1 className="text-3xl md:text-4xl font-bold text-[#A67B5B] mb-6 text-center">Plan du site</h1>
      <ul className="text-gray-700 mb-4 text-justify list-disc pl-6">
        <li><a href="/" className="text-[#A67B5B] hover:underline">Accueil</a></li>
        <li><a href="/marque" className="text-[#A67B5B] hover:underline">Nos marques</a></li>
        <li><a href="/Profile" className="text-[#A67B5B] hover:underline">Mon profil</a></li>
        <li><a href="/commandes" className="text-[#A67B5B] hover:underline">Mes commandes</a></li>
        <li><a href="/FavoritesPage" className="text-[#A67B5B] hover:underline">Mes favoris</a></li>
        <li><a href="/RetraitEnBoutique" className="text-[#A67B5B] hover:underline">Retrait en boutique</a></li>
        <li><a href="/ModesDePaiement" className="text-[#A67B5B] hover:underline">Modes de paiement</a></li>
        <li><a href="/CartesCadeaux" className="text-[#A67B5B] hover:underline">Cartes cadeaux</a></li>
        <li><a href="/NotreHistoire" className="text-[#A67B5B] hover:underline">Notre histoire</a></li>
        <li><a href="/ProfessionalPage" className="text-[#A67B5B] hover:underline">Espace pro</a></li>
        <li><a href="/DevenirAffilie" className="text-[#A67B5B] hover:underline">Devenir partenaire</a></li>
        <li><a href="/ConditionsGenerales" className="text-[#A67B5B] hover:underline">Conditions générales</a></li>
        <li><a href="/PolitiqueConfidentialite" className="text-[#A67B5B] hover:underline">Politique de confidentialité</a></li>
        <li><a href="/MentionsLegales" className="text-[#A67B5B] hover:underline">Mentions légales</a></li>
        <li><a href="/PlanDuSite" className="text-[#A67B5B] hover:underline">Plan du site</a></li>
        <li><a href="/FAQ" className="text-[#A67B5B] hover:underline">FAQ</a></li>
      </ul>
       <Chatbot />
      <div className="mt-8 text-center">
        <span className="text-xs text-gray-400">Dernière mise à jour : Mai 2025</span>
      </div>
    </div>
  </div>
);

export default PlanDuSite;
