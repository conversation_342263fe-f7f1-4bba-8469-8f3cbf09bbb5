
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for frontoffice-deploy-main/src/pages/PlanDuSite.jsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">frontoffice-deploy-main/src/pages</a> PlanDuSite.jsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/31</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/31</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import Chatbot from "@/Components/Chatbot";<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >import React from "react";</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const PlanDuSite = () =&gt; (</span>
<span class="cstat-no" title="statement not covered" >  &lt;div className="min-h-screen bg-gradient-to-br from-[#f7f3ef] to-[#fff] py-12 px-4 sm:px-8 md:px-20 lg:px-48"&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="max-w-3xl mx-auto bg-white rounded-2xl shadow-xl p-8 md:p-12"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;h1 className="text-3xl md:text-4xl font-bold text-[#A67B5B] mb-6 text-center"&gt;Plan du site&lt;/h1&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ul className="text-gray-700 mb-4 text-justify list-disc pl-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/" className="text-[#A67B5B] hover:underline"&gt;Accueil&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/marque" className="text-[#A67B5B] hover:underline"&gt;Nos marques&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/Profile" className="text-[#A67B5B] hover:underline"&gt;Mon profil&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/commandes" className="text-[#A67B5B] hover:underline"&gt;Mes commandes&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/FavoritesPage" className="text-[#A67B5B] hover:underline"&gt;Mes favoris&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/RetraitEnBoutique" className="text-[#A67B5B] hover:underline"&gt;Retrait en boutique&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/ModesDePaiement" className="text-[#A67B5B] hover:underline"&gt;Modes de paiement&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/CartesCadeaux" className="text-[#A67B5B] hover:underline"&gt;Cartes cadeaux&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/NotreHistoire" className="text-[#A67B5B] hover:underline"&gt;Notre histoire&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/ProfessionalPage" className="text-[#A67B5B] hover:underline"&gt;Espace pro&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/DevenirAffilie" className="text-[#A67B5B] hover:underline"&gt;Devenir partenaire&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/ConditionsGenerales" className="text-[#A67B5B] hover:underline"&gt;Conditions générales&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/PolitiqueConfidentialite" className="text-[#A67B5B] hover:underline"&gt;Politique de confidentialité&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/MentionsLegales" className="text-[#A67B5B] hover:underline"&gt;Mentions légales&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/PlanDuSite" className="text-[#A67B5B] hover:underline"&gt;Plan du site&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;li&gt;&lt;a href="/FAQ" className="text-[#A67B5B] hover:underline"&gt;FAQ&lt;/a&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/ul&gt;</span>
<span class="cstat-no" title="statement not covered" >       &lt;Chatbot /&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="mt-8 text-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;span className="text-xs text-gray-400"&gt;Dernière mise à jour : Mai 2025&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >  &lt;/div&gt;</span>
);
&nbsp;
<span class="cstat-no" title="statement not covered" >export default PlanDuSite;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-30T11:01:17.113Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    