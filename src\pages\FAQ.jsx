import Chatbot from "@/Components/Chatbot";
import React from "react";

const FAQ = () => (

 <div className="min-h-screen bg-gradient-to-br from-[#f7f3ef] to-[#fff] py-12 px-4 sm:px-8 md:px-20 lg:px-48">
    <div className="max-w-3xl mx-auto bg-white rounded-2xl shadow-xl p-8 md:p-12">
      <h1 className="text-3xl md:text-4xl font-bold text-[#A67B5B] mb-6 text-center">Foire aux questions (FAQ)</h1>
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-[#A67B5B] mb-2">Comment passer commande ?</h2>
        <p className="text-gray-700 mb-4 text-justify">Ajoutez les articles à votre panier, puis validez votre commande en suivant les instructions à l'écran.</p>
        <h2 className="text-lg font-semibold text-[#A67B5B] mb-2">Quels sont les moyens de paiement acceptés ?</h2>
        <p className="text-gray-700 mb-4 text-justify">Nous acceptons les paiements en ligne, par carte bancaire, et en boutique.</p>
        <h2 className="text-lg font-semibold text-[#A67B5B] mb-2">Puis-je retirer ma commande en boutique ?</h2>
        <p className="text-gray-700 mb-4 text-justify">Oui, le retrait en boutique est possible à l'adresse indiquée sur la page dédiée.</p>
        <h2 className="text-lg font-semibold text-[#A67B5B] mb-2">Comment contacter le service client ?</h2>
        <p className="text-gray-700 mb-4 text-justify">Par WhatsApp, téléphone ou email. Toutes les informations sont disponibles dans le pied de page du site.</p>
        <h2 className="text-lg font-semibold text-[#A67B5B] mb-2">Comment utiliser une carte cadeau ?</h2>
        <p className="text-gray-700 mb-4 text-justify">Saisissez le code de votre carte cadeau lors du paiement pour bénéficier de la réduction.</p>
      </div>
       <Chatbot />
      <div className="mt-8 text-center">
        <span className="text-xs text-gray-400">Dernière mise à jour : Mai 2025</span>
      </div>
    </div>
  </div>
);

export default FAQ;
