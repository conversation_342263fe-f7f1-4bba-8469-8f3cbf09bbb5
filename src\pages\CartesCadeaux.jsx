import Chatbot from "@/Components/Chatbot";
import React from "react";

export default function CartesCadeaux() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 flex flex-col items-center justify-center py-16 px-4">
      <div className="max-w-2xl w-full bg-white rounded-2xl shadow-xl p-10 relative overflow-hidden">
        <h1 className="text-3xl md:text-4xl font-light text-center mb-6 tracking-widest text-[#A67B5B]">Cartes cadeaux</h1>
        <div className="w-20 h-1 bg-[#A67B5B] mx-auto mb-8"></div>
        <Chatbot />
        <p className="text-lg text-gray-700 mb-6 text-center">
          Offrez le choix et l'élégance avec nos cartes cadeaux Ji-line & Carré Blanc Paris Sfax !
        </p>
        <ul className="mb-8 space-y-4">
          <li className="flex items-center">
            <span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
              <i className="fa-solid fa-gift text-[#A67B5B] text-lg"></i>
            </span>
            <span className="text-gray-800 font-medium">Montant au choix (à partir de 150 DT)</span>
          </li>
          <li className="flex items-center">
            <span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
              <i className="fa-solid fa-envelope-open-text text-[#A67B5B] text-lg"></i>
            </span>
            <span className="text-gray-800 font-medium">Carte digitale ou physique</span>
          </li>
          <li className="flex items-center">
            <span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
              <i className="fa-solid fa-store text-[#A67B5B] text-lg"></i>
            </span>
            <span className="text-gray-800 font-medium">Valable en boutique Carré Blanc Paris & J-line Sfax</span>
          </li>
          <li className="flex items-center">
            <span className="w-8 h-8 rounded-full bg-[#A67B5B]/10 flex items-center justify-center mr-3">
              <i className="fa-solid fa-calendar-check text-[#A67B5B] text-lg"></i>
            </span>
            <span className="text-gray-800 font-medium">Valable 12 mois à compter de la date d'achat</span>
          </li>
        </ul>
        <p className="text-center text-gray-500 text-sm mb-4">
          Pour offrir une carte cadeau ou en savoir plus, rendez-vous en boutique ou contactez notre équipe !
        </p>
        <div className="flex justify-center">
          <img src="/img/gift-card.png" alt="Carte cadeau Jihen-line" className="w-64 rounded-xl shadow-md" onError={e => e.target.style.display='none'} />
        </div>
      </div>
    </div>
  );
}
